# 换行符清理工具使用说明

## 功能描述
这个工具用于去除 `corpus_astro_en` 表中 `chart_content_json` 字段的所有换行符，保持JSON数据的完整性。

## 文件说明

### 1. remove_newlines.py
- 完整版本的换行符清理工具
- 包含数据备份功能
- 详细的错误处理和日志记录

### 2. clean_newlines.py
- 简化版本的换行符清理工具
- 专注于换行符去除功能
- 包含预览功能，可以在实际更新前查看要修改的数据

## 使用方法

### 方法一：使用简化版本（推荐）
```bash
python clean_newlines.py
```

### 方法二：使用完整版本
```bash
python remove_newlines.py
```

## 功能特点

1. **安全性**
   - 执行前会预览要修改的数据
   - 需要用户确认才会执行实际更新
   - 完整的事务处理，出错时自动回滚

2. **智能处理**
   - 自动检测有效的JSON格式
   - 对于有效JSON，使用标准库重新序列化
   - 对于无效JSON，直接去除换行符

3. **详细日志**
   - 记录处理过程和结果
   - 保存到日志文件便于查看
   - 实时显示处理进度

4. **验证功能**
   - 处理完成后自动验证结果
   - 确保所有换行符都已被去除

## 注意事项

1. 请确保数据库连接配置正确
2. 建议在非生产环境先测试
3. 处理大量数据时可能需要较长时间
4. 建议在数据库负载较低时执行

## 日志文件

- `clean_newlines.log` - 简化版本的日志
- `remove_newlines.log` - 完整版本的日志

## 数据库配置

工具使用以下数据库配置：
- 主机: 59.110.52.91
- 端口: 13956
- 用户: horoscope_rwu
- 数据库: horoscope_prod

如需修改配置，请编辑脚本中的 `DB_CONFIG` 变量。