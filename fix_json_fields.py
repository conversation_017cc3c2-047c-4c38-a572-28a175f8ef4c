#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复 corpus_en 表中无效的JSON字段，将其设置为null
"""

import mysql.connector
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_json_fields.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class JsonFieldFixer:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4'
        }
        
        # 需要检查的JSON字段列表
        self.json_fields = [
            'new_content',
            'transit_chart_content',
            'combination_chart_content',
            'thirdprogressed_chart_content',
            'secondarylimit_chart_content',
            'lunarreturn_chart_content',
            'solarreturn_chart_content',
            'solararc_chart_content',
            'developed_chart_content',
            'smalllimit_chart_content',
            'nataltwelvepointer_chart_content',
            'natalthirteenpointer_chart_content',
            'current_chart_content',
            'chart_content_markdown',
            'comparision_a_chart_content',
            'comparision_b_chart_content',
            'compositeThirprogr_chart_content',
            'compositesecondary_chart_content',
            'marks_a_chart_content',
            'marks_b_chart_content',
            'marksthirprogr_a_chart_content',
            'marksthirprogr_b_chart_content',
            'markssecprogr_a_chart_content',
            'markssecprogr_b_chart_content',
            'timesmidpoint_chart_content',
            'timesmidpointthirprogr_chart_content',
            'timesmidpointsecprogr_chart_content'
        ]
        
        # 数据库连接
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def is_valid_json(self, text):
        """检查文本是否为有效的JSON格式"""
        if not text or text.strip() == '':
            return True  # 空值或空字符串认为是有效的
        
        try:
            json.loads(text)
            return True
        except (json.JSONDecodeError, TypeError, ValueError):
            return False
    
    def fix_all_invalid_json(self):
        """批量修复所有无效的JSON字段"""
        try:
            logger.info("开始批量修复无效JSON字段...")
            
            # 获取总记录数
            self.cursor.execute("SELECT COUNT(*) as total FROM corpus_en")
            total_records = self.cursor.fetchone()['total']
            logger.info(f"总记录数: {total_records}")
            
            # 分批处理记录
            batch_size = 100
            offset = 0
            total_fixed_records = 0
            total_fixed_fields = 0
            
            while offset < total_records:
                # 获取一批记录
                fields_str = ', '.join(['id'] + self.json_fields)
                query = f"SELECT {fields_str} FROM corpus_en ORDER BY id LIMIT {batch_size} OFFSET {offset}"
                
                self.cursor.execute(query)
                records = self.cursor.fetchall()
                
                if not records:
                    break
                
                logger.info(f"处理第 {offset + 1} - {offset + len(records)} 条记录")
                
                # 处理这批记录
                for record in records:
                    record_id = record['id']
                    invalid_fields = []
                    
                    # 检查每个JSON字段
                    for field in self.json_fields:
                        field_value = record.get(field)
                        if field_value and not self.is_valid_json(field_value):
                            invalid_fields.append(field)
                    
                    # 如果有无效字段，则修复
                    if invalid_fields:
                        try:
                            # 构建UPDATE语句
                            update_parts = [f"{field} = NULL" for field in invalid_fields]
                            update_query = f"""
                            UPDATE corpus_en 
                            SET {', '.join(update_parts)}
                            WHERE id = %s
                            """
                            
                            self.cursor.execute(update_query, (record_id,))
                            
                            logger.info(f"修复记录 {record_id}: 设置 {len(invalid_fields)} 个字段为NULL ({', '.join(invalid_fields)})")
                            total_fixed_records += 1
                            total_fixed_fields += len(invalid_fields)
                            
                        except Exception as e:
                            logger.error(f"修复记录 {record_id} 失败: {e}")
                
                # 提交这批修改
                self.connection.commit()
                logger.info(f"已提交第 {offset + 1} - {offset + len(records)} 条记录的修改")
                
                offset += len(records)
            
            logger.info(f"批量修复完成！")
            logger.info(f"修复的记录数: {total_fixed_records}")
            logger.info(f"修复的字段数: {total_fixed_fields}")
            
        except Exception as e:
            logger.error(f"批量修复失败: {e}")
            if self.connection:
                self.connection.rollback()
    
    def preview_invalid_records(self, limit=10):
        """预览包含无效JSON的记录"""
        try:
            logger.info(f"预览前 {limit} 条包含无效JSON的记录...")

            # 使用更高效的方法：逐个字段检查，而不是一次性加载所有字段
            found_count = 0
            offset = 0
            batch_size = 50

            while found_count < limit:
                # 只获取id和一个字段进行快速检查
                query = f"SELECT id, new_content FROM corpus_en WHERE new_content IS NOT NULL AND new_content != '' ORDER BY id LIMIT {batch_size} OFFSET {offset}"

                self.cursor.execute(query)
                records = self.cursor.fetchall()

                if not records:
                    break

                for record in records:
                    if found_count >= limit:
                        break

                    record_id = record['id']

                    # 检查这条记录的所有JSON字段
                    fields_str = ', '.join(['id'] + self.json_fields)
                    detail_query = f"SELECT {fields_str} FROM corpus_en WHERE id = %s"
                    self.cursor.execute(detail_query, (record_id,))
                    detail_record = self.cursor.fetchone()

                    if not detail_record:
                        continue

                    invalid_fields = []
                    for field in self.json_fields:
                        field_value = detail_record.get(field)
                        if field_value and not self.is_valid_json(field_value):
                            invalid_fields.append(field)

                    if invalid_fields:
                        logger.info(f"ID: {record_id}, 无效JSON字段 ({len(invalid_fields)}): {', '.join(invalid_fields)}")

                        # 显示第一个无效字段的内容示例
                        first_invalid_field = invalid_fields[0]
                        field_value = detail_record.get(first_invalid_field, '')
                        preview_text = str(field_value)[:100] + ('...' if len(str(field_value)) > 100 else '')
                        logger.info(f"  示例内容 ({first_invalid_field}): {preview_text}")
                        logger.info("-" * 50)

                        found_count += 1

                offset += batch_size

                # 避免无限循环
                if offset > 10000:  # 最多检查10000条记录
                    break

            if found_count == 0:
                logger.info("没有找到包含无效JSON的记录")
            else:
                logger.info(f"找到 {found_count} 条包含无效JSON的记录")

        except Exception as e:
            logger.error(f"预览失败: {e}")

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("批量修复 corpus_en 表中无效的JSON字段")
    logger.info(f"开始时间: {datetime.now()}")
    logger.info("=" * 60)
    
    fixer = JsonFieldFixer()
    
    if not fixer.connect_database():
        return
    
    try:
        print("\n请选择操作:")
        print("1. 预览包含无效JSON的记录")
        print("2. 批量修复所有无效JSON字段")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-2): ").strip()
        
        if choice == '1':
            limit = input("输入预览记录数 (默认10): ").strip()
            limit = int(limit) if limit.isdigit() else 10
            fixer.preview_invalid_records(limit)
            
        elif choice == '2':
            print(f"\n⚠️  警告：这将修改数据库，将所有无效JSON字段设置为NULL")
            print("建议先运行选项1预览要修复的记录")
            confirm = input("\n确认继续批量修复？(y/n): ").strip().lower()
            
            if confirm in ['y', 'yes', '是']:
                fixer.fix_all_invalid_json()
            else:
                logger.info("操作已取消")
                
        elif choice == '0':
            logger.info("退出程序")
            
        else:
            logger.error("无效的选择")
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    finally:
        fixer.close_database()
    
    logger.info(f"结束时间: {datetime.now()}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
