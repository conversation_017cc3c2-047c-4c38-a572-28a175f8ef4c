#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
去除 corpus_astro_en 表中 chart_content_json 字段的换行符
"""

import mysql.connector
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('remove_newlines.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': '************',
    'port': 13956,
    'user': 'horoscope_rwu',
    'password': 'h46eaN6JoQxo0VIe',
    'database': 'horoscope_prod',
    'charset': 'utf8mb4'
}

def remove_newlines_from_json(json_str):
    """
    去除JSON字符串中的换行符
    """
    if not json_str:
        return json_str
    
    # 去除所有类型的换行符
    cleaned_str = json_str.replace('\n', '').replace('\r', '').replace('\r\n', '')
    return cleaned_str

def update_chart_content_json():
    """
    更新 corpus_astro_en 表中的 chart_content_json 字段，去除换行符
    """
    connection = None
    cursor = None
    
    try:
        # 连接数据库
        logger.info("正在连接数据库...")
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 查询需要更新的记录
        select_query = """
        SELECT id, chart_content_json 
        FROM corpus_astro_en 
        WHERE chart_content_json IS NOT NULL 
        AND (chart_content_json LIKE '%\\n%' OR chart_content_json LIKE '%\\r%')
        """
        
        cursor.execute(select_query)
        records = cursor.fetchall()
        
        if not records:
            logger.info("没有找到包含换行符的记录")
            return
        
        logger.info(f"找到 {len(records)} 条包含换行符的记录")
        
        # 准备更新语句
        update_query = """
        UPDATE corpus_astro_en 
        SET chart_content_json = %s 
        WHERE id = %s
        """
        
        updated_count = 0
        error_count = 0
        
        # 逐条处理记录
        for record_id, chart_content_json in records:
            try:
                # 去除换行符
                cleaned_json = remove_newlines_from_json(chart_content_json)
                
                # 验证JSON格式是否仍然有效
                if cleaned_json:
                    try:
                        json.loads(cleaned_json)
                    except json.JSONDecodeError:
                        logger.warning(f"记录 ID {record_id}: 清理后的JSON格式无效，跳过")
                        error_count += 1
                        continue
                
                # 更新数据库
                cursor.execute(update_query, (cleaned_json, record_id))
                updated_count += 1
                
                if updated_count % 100 == 0:
                    logger.info(f"已处理 {updated_count} 条记录...")
                    
            except Exception as e:
                logger.error(f"处理记录 ID {record_id} 时出错: {str(e)}")
                error_count += 1
                continue
        
        # 提交事务
        connection.commit()
        
        logger.info(f"更新完成！成功更新 {updated_count} 条记录，错误 {error_count} 条")
        
        # 验证更新结果
        verify_query = """
        SELECT COUNT(*) 
        FROM corpus_astro_en 
        WHERE chart_content_json IS NOT NULL 
        AND (chart_content_json LIKE '%\\n%' OR chart_content_json LIKE '%\\r%')
        """
        
        cursor.execute(verify_query)
        remaining_count = cursor.fetchone()[0]
        
        if remaining_count == 0:
            logger.info("验证成功：所有换行符已被去除")
        else:
            logger.warning(f"验证发现仍有 {remaining_count} 条记录包含换行符")
            
    except mysql.connector.Error as e:
        logger.error(f"数据库错误: {e}")
        if connection:
            connection.rollback()
        raise
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        if connection:
            connection.rollback()
        raise
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("数据库连接已关闭")

def main():
    """
    主函数
    """
    logger.info("开始去除 corpus_astro_en 表中 chart_content_json 字段的换行符")
    logger.info(f"开始时间: {datetime.now()}")
    
    try:
        update_chart_content_json()
        logger.info("任务完成！")
        
    except Exception as e:
        logger.error(f"任务失败: {e}")
        return 1
    
    logger.info(f"结束时间: {datetime.now()}")
    return 0

if __name__ == "__main__":
    exit(main())