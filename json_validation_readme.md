# JSON字段验证和修复工具

这些工具用于检查和修复 `corpus_en` 表中指定字段的JSON格式有效性。

## 涉及的字段

以下字段将被检查JSON格式有效性：

- `new_content` - 本命盘-语料
- `transit_chart_content` - 行运盘-语料
- `combination_chart_content` - 组合盘-语料
- `thirdprogressed_chart_content` - 三限盘-语料
- `secondarylimit_chart_content` - 次限盘-语料
- `lunarreturn_chart_content` - 月返照-语料
- `solarreturn_chart_content` - 日返照-语料
- `solararc_chart_content` - 太阳弧-语料
- `developed_chart_content` - 法达盘-语料
- `smalllimit_chart_content` - 小限盘-语料
- `nataltwelvepointer_chart_content` - 十二分盘-语料
- `natalthirteenpointer_chart_content` - 十三分盘-语料
- `current_chart_content` - 天象盘-语料
- `chart_content_markdown` - markdown-语料
- `comparision_a_chart_content` - 比较盘-a-语料
- `comparision_b_chart_content` - 比较盘-b-语料
- `compositeThirprogr_chart_content` - 组合三限盘
- `compositesecondary_chart_content` - 组合次限盘
- `marks_a_chart_content` - 马克思盘-a-语料
- `marks_b_chart_content` - 马克思盘-b-语料
- `marksthirprogr_a_chart_content` - 马盘三限盘-a-语料
- `marksthirprogr_b_chart_content` - 马盘三限盘-b-语料
- `markssecprogr_a_chart_content` - 马盘次限盘-a-语料
- `markssecprogr_b_chart_content` - 马盘次限盘-b-语料
- `timesmidpoint_chart_content` - 时空盘-语料
- `timesmidpointthirprogr_chart_content` - 时空三限盘-语料
- `timesmidpointsecprogr_chart_content` - 时空次限盘-语料

## 工具说明

### 1. validate_json_fields.py - 完整的验证工具

这是一个功能完整的交互式工具，提供以下功能：

- **预览无效记录**: 查看包含无效JSON的记录
- **统计信息**: 获取所有JSON字段的统计信息
- **验证特定记录**: 检查指定ID记录的JSON字段
- **预演修复**: 模拟修复过程，不实际修改数据
- **实际修复**: 将无效JSON字段设置为NULL

### 2. fix_json_fields.py - 简化的批量修复工具

这是一个专门用于批量修复的简化工具，提供：

- **预览功能**: 快速查看无效记录
- **批量修复**: 一次性修复所有无效JSON字段

## 使用方法

### 安装依赖

```bash
pip install mysql-connector-python
```

### 运行完整验证工具

```bash
python validate_json_fields.py
```

运行后会显示菜单：
```
请选择操作:
1. 预览包含无效JSON的记录
2. 获取JSON字段统计信息
3. 验证特定记录
4. 预演修复无效JSON字段
5. 实际修复无效JSON字段
0. 退出
```

### 运行批量修复工具

```bash
python fix_json_fields.py
```

运行后会显示菜单：
```
请选择操作:
1. 预览包含无效JSON的记录
2. 批量修复所有无效JSON字段
0. 退出
```

## 使用建议

1. **首次使用建议流程**：
   - 先运行 `validate_json_fields.py` 选择选项2获取统计信息
   - 然后选择选项1预览一些无效记录
   - 如果确认需要修复，选择选项4进行预演
   - 最后选择选项5进行实际修复

2. **快速修复流程**：
   - 运行 `fix_json_fields.py`
   - 先选择选项1预览要修复的记录
   - 确认无误后选择选项2进行批量修复

## 修复逻辑

- **空值处理**: 空字符串或NULL值被认为是有效的
- **JSON验证**: 使用Python的`json.loads()`方法验证JSON格式
- **修复方式**: 将无效的JSON字段设置为NULL
- **批量处理**: 每次处理100条记录，避免内存问题
- **事务安全**: 每批记录处理完成后提交事务

## 日志文件

- `validate_json_fields.log` - 完整验证工具的日志
- `fix_json_fields.log` - 批量修复工具的日志

## 注意事项

1. **数据备份**: 在执行实际修复前，建议备份相关数据
2. **测试环境**: 建议先在测试环境中验证脚本功能
3. **网络连接**: 确保数据库连接稳定
4. **权限检查**: 确保数据库用户有UPDATE权限
5. **监控日志**: 执行过程中注意查看日志输出

## 错误处理

- 数据库连接失败会自动重试
- 单条记录处理失败不会影响其他记录
- 所有错误都会记录到日志文件中
- 支持用户中断操作（Ctrl+C）

## 性能考虑

- 使用批量处理避免内存溢出
- 每批处理后提交事务保证数据一致性
- 合理的批次大小（100条记录）平衡性能和内存使用
