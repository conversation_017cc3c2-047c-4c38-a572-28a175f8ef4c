#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取特定记录特定字段的完整内容
"""

import mysql.connector
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def connect_database():
    """连接数据库"""
    db_config = {
        'host': '************',
        'port': 13956,
        'user': 'horoscope_rwu',
        'password': 'h46eaN6JoQxo0VIe',
        'database': 'horoscope_prod',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        logger.info("数据库连接成功")
        return connection, cursor
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None, None

def get_field_content(record_id, field_name):
    """获取特定字段的完整内容"""
    connection, cursor = connect_database()
    if not connection:
        return
    
    try:
        # 获取记录
        query = f"SELECT id, {field_name} FROM corpus_en WHERE id = %s"
        
        cursor.execute(query, (record_id,))
        record = cursor.fetchone()
        
        if not record:
            logger.error(f"未找到ID为 {record_id} 的记录")
            return
        
        field_value = record.get(field_name)
        
        if field_value is None:
            logger.info(f"记录 {record_id} 的字段 {field_name} 为 NULL")
            return
        
        if field_value == '':
            logger.info(f"记录 {record_id} 的字段 {field_name} 为空字符串")
            return
        
        logger.info(f"记录 {record_id} 的字段 {field_name} 内容:")
        logger.info("=" * 80)
        print(field_value)
        logger.info("=" * 80)
        
        # 尝试解析JSON并显示错误
        try:
            parsed = json.loads(field_value)
            logger.info("✅ JSON格式有效")
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON格式无效: {e}")
            logger.error(f"错误位置: 行 {e.lineno}, 列 {e.colno}")
            
            # 显示错误附近的内容
            lines = field_value.split('\n')
            if e.lineno <= len(lines):
                error_line = lines[e.lineno - 1]
                logger.error(f"错误行内容: {error_line}")
                
                # 显示错误位置
                if e.colno <= len(error_line):
                    pointer = ' ' * (e.colno - 1) + '^'
                    logger.error(f"错误位置: {pointer}")
        
    except Exception as e:
        logger.error(f"获取字段内容失败: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("数据库连接已关闭")

def main():
    """主函数"""
    record_id = 144
    field_name = 'secondarylimit_chart_content'
    
    logger.info(f"获取记录 {record_id} 的字段 {field_name} 完整内容")
    get_field_content(record_id, field_name)

if __name__ == "__main__":
    main()
