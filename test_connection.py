#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接和API连接
"""

import mysql.connector
import requests
import json

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    
    db_config = {
        'host': '************',
        'port': 13956,
        'user': 'horoscope_rwu',
        'password': 'h46eaN6JoQxo0VIe',
        'database': 'horoscope_prod',  # 使用生产数据库
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        
        # 测试查询
        cursor.execute("SELECT COUNT(*) as total FROM corpus_astro_en")
        result = cursor.fetchone()
        
        print(f"✅ 数据库连接成功！")
        print(f"📊 corpus_astro_en 表总记录数: {result['total']}")
        
        # 查询包含中文的记录数量
        cursor.execute("""
            SELECT COUNT(*) as chinese_count 
            FROM corpus_astro_en 
            WHERE title REGEXP '[\\u4e00-\\u9fff]'
               OR sentence REGEXP '[\\u4e00-\\u9fff]'
               OR chart_content_json REGEXP '[\\u4e00-\\u9fff]'
               OR chart_content_markdown REGEXP '[\\u4e00-\\u9fff]'
        """)
        chinese_result = cursor.fetchone()
        print(f"🈶 包含中文的记录数: {chinese_result['chinese_count']}")
        
        # 查看表结构
        cursor.execute("DESCRIBE corpus_astro_en")
        columns = cursor.fetchall()
        print(f"📋 表字段: {[col['Field'] for col in columns]}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_deepseek_api():
    """测试DeepSeek API连接"""
    print("\n=== 测试DeepSeek API连接 ===")
    
    api_key = '***********************************'
    api_url = 'https://api.deepseek.com/v1/chat/completions'
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    data = {
        'model': 'deepseek-chat',
        'messages': [
            {
                'role': 'user',
                'content': '请将"你好世界"翻译成英文'
            }
        ],
        'temperature': 0.3,
        'max_tokens': 100
    }
    
    try:
        response = requests.post(api_url, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        translated_text = result['choices'][0]['message']['content'].strip()
        
        print(f"✅ DeepSeek API连接成功！")
        print(f"🔤 测试翻译结果: {translated_text}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    print("🚀 开始测试连接配置...\n")
    
    db_success = test_database_connection()
    api_success = test_deepseek_api()
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"数据库连接: {'✅ 成功' if db_success else '❌ 失败'}")
    print(f"API连接: {'✅ 成功' if api_success else '❌ 失败'}")
    
    if db_success and api_success:
        print(f"\n🎉 所有连接测试通过，可以开始翻译任务！")
        print(f"运行命令: python run_translation.py --action translate")
    else:
        print(f"\n⚠️  请检查失败的连接配置")

if __name__ == "__main__":
    main()