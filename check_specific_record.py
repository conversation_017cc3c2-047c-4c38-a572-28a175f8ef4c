#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查特定记录的JSON字段
"""

import mysql.connector
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def connect_database():
    """连接数据库"""
    db_config = {
        'host': '************',
        'port': 13956,
        'user': 'horoscope_rwu',
        'password': 'h46eaN6JoQxo0VIe',
        'database': 'horoscope_prod',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        logger.info("数据库连接成功")
        return connection, cursor
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None, None

def is_valid_json(text):
    """检查文本是否为有效的JSON格式"""
    if not text or text.strip() == '':
        return True  # 空值或空字符串认为是有效的
    
    try:
        json.loads(text)
        return True
    except (json.JSONDecodeError, TypeError, ValueError):
        return False

def check_record(record_id):
    """检查特定记录"""
    connection, cursor = connect_database()
    if not connection:
        return
    
    try:
        # JSON字段列表
        json_fields = [
            'new_content',
            'transit_chart_content',
            'combination_chart_content',
            'thirdprogressed_chart_content',
            'secondarylimit_chart_content',
            'lunarreturn_chart_content',
            'solarreturn_chart_content',
            'solararc_chart_content',
            'developed_chart_content',
            'smalllimit_chart_content',
            'nataltwelvepointer_chart_content',
            'natalthirteenpointer_chart_content',
            'current_chart_content',
            'chart_content_markdown',
            'comparision_a_chart_content',
            'comparision_b_chart_content',
            'compositeThirprogr_chart_content',
            'compositesecondary_chart_content',
            'marks_a_chart_content',
            'marks_b_chart_content',
            'marksthirprogr_a_chart_content',
            'marksthirprogr_b_chart_content',
            'markssecprogr_a_chart_content',
            'markssecprogr_b_chart_content',
            'timesmidpoint_chart_content',
            'timesmidpointthirprogr_chart_content',
            'timesmidpointsecprogr_chart_content'
        ]
        
        # 获取记录
        fields_str = ', '.join(['id'] + json_fields)
        query = f"SELECT {fields_str} FROM corpus_en WHERE id = %s"
        
        cursor.execute(query, (record_id,))
        record = cursor.fetchone()
        
        if not record:
            logger.error(f"未找到ID为 {record_id} 的记录")
            return
        
        logger.info(f"检查记录 ID: {record_id}")
        logger.info("=" * 80)
        
        for field in json_fields:
            field_value = record.get(field)
            
            if field_value is None:
                logger.info(f"{field}: NULL")
            elif field_value == '':
                logger.info(f"{field}: 空字符串")
            else:
                is_valid = is_valid_json(field_value)
                status = "✅ 有效JSON" if is_valid else "❌ 无效JSON"
                logger.info(f"{field}: {status}")
                
                if not is_valid:
                    # 显示无效内容的前500个字符
                    preview_text = str(field_value)[:500] + ('...' if len(str(field_value)) > 500 else '')
                    logger.info(f"  内容预览: {preview_text}")
                    logger.info("-" * 80)
        
    except Exception as e:
        logger.error(f"检查记录失败: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("数据库连接已关闭")

def main():
    """主函数"""
    record_id = 144
    logger.info(f"检查记录 ID {record_id} 的JSON字段")
    check_record(record_id)

if __name__ == "__main__":
    main()
