import mysql.connector
import json
import re
import time
import logging
from typing import Dict, Any, Optional, Tuple
import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('translation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseTranslator:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',  # 使用生产数据库
            'charset': 'utf8mb4'
        }
        
        # DeepSeek API配置
        self.api_key = '***********************************'
        self.api_url = 'https://api.deepseek.com/v1/chat/completions'
        
        # 需要翻译的字段
        self.fields_to_translate = ['title', 'sentence', 'chart_content_json', 'chart_content_markdown']
        
        # 连接数据库
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def has_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        if not text:
            return False
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))
    
    def clean_translation_result(self, text: str, field_name: str) -> str:
        """清理翻译结果，移除不必要的标记"""
        if not text:
            return text
        
        # 移除代码块标记
        if field_name in ['chart_content_json']:
            # 移除 ```json 和 ``` 标记
            text = re.sub(r'^```json\s*\n?', '', text, flags=re.IGNORECASE)
            text = re.sub(r'\n?```\s*$', '', text)
        elif field_name in ['chart_content_markdown']:
            # 移除 ```markdown 和 ``` 标记
            text = re.sub(r'^```markdown\s*\n?', '', text, flags=re.IGNORECASE)
            text = re.sub(r'\n?```\s*$', '', text)
        
        return text.strip()
    
    def call_deepseek_api(self, text: str, field_name: str) -> Optional[str]:
        """调用DeepSeek API进行翻译"""
        if not text or not self.has_chinese(text):
            return text
        
        # 根据字段类型选择不同的翻译提示词
        if field_name in ['chart_content_json']:
            prompt = f"""将以下JSON内容中的中文翻译成英文，保持JSON格式完全不变，只翻译值中的中文部分。直接返回翻译后的JSON内容，不要```json标记，不要任何解释：

{text}"""
        
        elif field_name in ['chart_content_markdown']:
            prompt = f"""将以下Markdown内容中的中文翻译成英文，保持Markdown格式完全不变。直接返回翻译后的Markdown内容，不要```markdown标记，不要任何解释：

{text}"""
        
        else:
            prompt = f"""将以下中文翻译成英文，如果是占星学术语请使用专业词汇。只返回翻译结果，不要任何解释：

{text}"""
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'deepseek-chat',
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.3,
            'max_tokens': 4000
        }
        
        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            translated_text = result['choices'][0]['message']['content'].strip()
            
            logger.info(f"翻译成功 - 字段: {field_name}, 原文长度: {len(text)}, 译文长度: {len(translated_text)}")
            return translated_text
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"翻译过程出错: {e}")
            return None
    
    def get_untranslated_records(self) -> list:
        """获取未翻译的记录"""
        try:
            # 查询包含中文的记录，或者translation_status不是'completed'的记录
            query = """
            SELECT id, title, sentence, chart_content_json, chart_content_markdown, 
                   translation_status, translation_progress
            FROM corpus_astro_en 
            WHERE translation_status IS NULL 
               OR translation_status != 'completed'
               OR title REGEXP '[\\u4e00-\\u9fff]'
               OR sentence REGEXP '[\\u4e00-\\u9fff]'
               OR chart_content_json REGEXP '[\\u4e00-\\u9fff]'
               OR chart_content_markdown REGEXP '[\\u4e00-\\u9fff]'
            ORDER BY id
            """
            
            self.cursor.execute(query)
            records = self.cursor.fetchall()
            logger.info(f"找到 {len(records)} 条需要翻译的记录")
            return records
            
        except Exception as e:
            logger.error(f"查询未翻译记录失败: {e}")
            return []
    
    def update_translation_status(self, record_id: int, status: str, progress: str = None):
        """更新翻译状态"""
        try:
            if progress:
                query = """
                UPDATE corpus_astro_en 
                SET translation_status = %s, translation_progress = %s, 
                    translation_updated_at = NOW()
                WHERE id = %s
                """
                self.cursor.execute(query, (status, progress, record_id))
            else:
                query = """
                UPDATE corpus_astro_en 
                SET translation_status = %s, translation_updated_at = NOW()
                WHERE id = %s
                """
                self.cursor.execute(query, (status, record_id))
            
            self.connection.commit()
            
        except Exception as e:
            logger.error(f"更新翻译状态失败: {e}")
    
    def translate_record(self, record: Dict[str, Any]) -> bool:
        """翻译单条记录"""
        record_id = record['id']
        logger.info(f"开始翻译记录 ID: {record_id}")
        
        # 标记为处理中
        self.update_translation_status(record_id, 'processing')
        
        translated_fields = {}
        progress_info = []
        
        for field in self.fields_to_translate:
            field_value = record.get(field)
            
            if not field_value:
                progress_info.append(f"{field}:empty")
                continue
            
            if not self.has_chinese(str(field_value)):
                progress_info.append(f"{field}:no_chinese")
                continue
            
            logger.info(f"翻译字段: {field}")
            translated_text = self.call_deepseek_api(str(field_value), field)
            
            if translated_text is not None:
                translated_fields[field] = translated_text
                progress_info.append(f"{field}:translated")
                logger.info(f"字段 {field} 翻译完成")
                
                # 每个字段翻译后稍作延迟，避免API限制
                time.sleep(1)
            else:
                progress_info.append(f"{field}:failed")
                logger.error(f"字段 {field} 翻译失败")
        
        # 更新翻译结果到数据库
        if translated_fields:
            try:
                update_parts = []
                update_values = []
                
                for field, translated_text in translated_fields.items():
                    update_parts.append(f"{field} = %s")
                    update_values.append(translated_text)
                
                update_values.append(record_id)
                
                query = f"""
                UPDATE corpus_astro_en 
                SET {', '.join(update_parts)}, translation_updated_at = NOW()
                WHERE id = %s
                """
                
                self.cursor.execute(query, update_values)
                self.connection.commit()
                
                # 更新为完成状态
                progress_str = ','.join(progress_info)
                self.update_translation_status(record_id, 'completed', progress_str)
                
                logger.info(f"记录 {record_id} 翻译完成并保存")
                return True
                
            except Exception as e:
                logger.error(f"保存翻译结果失败: {e}")
                self.update_translation_status(record_id, 'failed', f"save_error:{str(e)}")
                return False
        else:
            # 没有需要翻译的内容
            progress_str = ','.join(progress_info)
            self.update_translation_status(record_id, 'completed', progress_str)
            logger.info(f"记录 {record_id} 无需翻译")
            return True
    
    def add_translation_columns(self):
        """添加翻译状态跟踪字段"""
        try:
            # 检查字段是否已存在
            self.cursor.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'corpus_astro_en' 
                AND COLUMN_NAME IN ('translation_status', 'translation_progress', 'translation_updated_at')
            """, (self.db_config['database'],))
            
            existing_columns = [row['COLUMN_NAME'] for row in self.cursor.fetchall()]
            
            if 'translation_status' not in existing_columns:
                self.cursor.execute("""
                    ALTER TABLE corpus_astro_en 
                    ADD COLUMN translation_status VARCHAR(20) DEFAULT NULL
                """)
                logger.info("添加 translation_status 字段")
            
            if 'translation_progress' not in existing_columns:
                self.cursor.execute("""
                    ALTER TABLE corpus_astro_en 
                    ADD COLUMN translation_progress TEXT DEFAULT NULL
                """)
                logger.info("添加 translation_progress 字段")
            
            if 'translation_updated_at' not in existing_columns:
                self.cursor.execute("""
                    ALTER TABLE corpus_astro_en 
                    ADD COLUMN translation_updated_at TIMESTAMP DEFAULT NULL
                """)
                logger.info("添加 translation_updated_at 字段")
            
            self.connection.commit()
            
        except Exception as e:
            logger.error(f"添加翻译状态字段失败: {e}")
    
    def run_translation(self):
        """运行翻译任务"""
        if not self.connect_database():
            return
        
        try:
            # 添加翻译状态跟踪字段
            self.add_translation_columns()
            
            # 获取需要翻译的记录
            records = self.get_untranslated_records()
            
            if not records:
                logger.info("没有需要翻译的记录")
                return
            
            logger.info(f"开始翻译 {len(records)} 条记录")
            
            success_count = 0
            failed_count = 0
            
            for i, record in enumerate(records, 1):
                logger.info(f"处理进度: {i}/{len(records)}")
                
                try:
                    if self.translate_record(record):
                        success_count += 1
                    else:
                        failed_count += 1
                        
                    # 每处理10条记录后稍作休息
                    if i % 10 == 0:
                        logger.info(f"已处理 {i} 条记录，休息5秒...")
                        time.sleep(5)
                        
                except Exception as e:
                    logger.error(f"处理记录 {record['id']} 时出错: {e}")
                    failed_count += 1
                    continue
            
            logger.info(f"翻译任务完成！成功: {success_count}, 失败: {failed_count}")
            
        except Exception as e:
            logger.error(f"翻译任务执行失败: {e}")
        finally:
            self.close_database()

def main():
    translator = DatabaseTranslator()
    translator.run_translation()

if __name__ == "__main__":
    main()