#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库翻译任务运行脚本
支持断点续传功能
"""

import sys
import os
import argparse
from translate_database import DatabaseTranslator

def check_translation_status(translator):
    """检查翻译状态"""
    if not translator.connect_database():
        return
    
    try:
        # 查询翻译状态统计
        translator.cursor.execute("""
            SELECT 
                translation_status,
                COUNT(*) as count
            FROM corpus_astro_en 
            GROUP BY translation_status
        """)
        
        status_stats = translator.cursor.fetchall()
        
        print("\n=== 翻译状态统计 ===")
        for stat in status_stats:
            status = stat['translation_status'] or 'NULL'
            count = stat['count']
            print(f"{status}: {count} 条记录")
        
        # 查询最近的翻译记录
        translator.cursor.execute("""
            SELECT id, translation_status, translation_progress, translation_updated_at
            FROM corpus_astro_en 
            WHERE translation_updated_at IS NOT NULL
            ORDER BY translation_updated_at DESC
            LIMIT 10
        """)
        
        recent_records = translator.cursor.fetchall()
        
        print("\n=== 最近翻译的记录 ===")
        for record in recent_records:
            print(f"ID: {record['id']}, 状态: {record['translation_status']}, "
                  f"进度: {record['translation_progress']}, "
                  f"更新时间: {record['translation_updated_at']}")
        
    except Exception as e:
        print(f"查询翻译状态失败: {e}")
    finally:
        translator.close_database()

def reset_failed_records(translator):
    """重置失败的记录状态"""
    if not translator.connect_database():
        return
    
    try:
        translator.cursor.execute("""
            UPDATE corpus_astro_en 
            SET translation_status = NULL, translation_progress = NULL
            WHERE translation_status = 'failed'
        """)
        
        affected_rows = translator.cursor.rowcount
        translator.connection.commit()
        
        print(f"已重置 {affected_rows} 条失败记录的状态")
        
    except Exception as e:
        print(f"重置失败记录状态失败: {e}")
    finally:
        translator.close_database()

def main():
    parser = argparse.ArgumentParser(description='数据库翻译工具')
    parser.add_argument('--action', choices=['translate', 'status', 'reset-failed'], 
                       default='translate', help='执行的操作')
    
    args = parser.parse_args()
    
    translator = DatabaseTranslator()
    
    if args.action == 'translate':
        print("开始执行翻译任务...")
        translator.run_translation()
    elif args.action == 'status':
        print("查询翻译状态...")
        check_translation_status(translator)
    elif args.action == 'reset-failed':
        print("重置失败记录...")
        reset_failed_records(translator)

if __name__ == "__main__":
    main()