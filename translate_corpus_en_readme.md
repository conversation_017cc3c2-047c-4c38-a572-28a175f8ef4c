# corpus_en 表翻译工具使用说明

## 功能描述
这个工具用于翻译 `corpus_en` 表中从 `content` 字段开始的所有字段，支持不同类型的内容格式。

## 表结构说明
`corpus_en` 表包含以下需要翻译的字段：

### 字段分类：
1. **富文本字段**：
   - `content` - 富文本内容，包含HTML标签

2. **JSON字段**：
   - `new_content` - 本命盘语料
   - `transit_chart_content` - 行运盘语料
   - `combination_chart_content` - 组合盘语料
   - `thirdprogressed_chart_content` - 三限盘语料
   - `secondarylimit_chart_content` - 次限盘语料
   - `lunarreturn_chart_content` - 月返照语料
   - `solarreturn_chart_content` - 日返照语料
   - `solararc_chart_content` - 太阳弧语料
   - `developed_chart_content` - 法达盘语料
   - `smalllimit_chart_content` - 小限盘语料
   - `nataltwelvepointer_chart_content` - 十二分盘语料
   - `natalthirteenpointer_chart_content` - 十三分盘语料
   - `current_chart_content` - 天象盘语料
   - `comparision_a_chart_content` - 比较盘-a语料
   - `comparision_b_chart_content` - 比较盘-b语料
   - `compositeThirprogr_chart_content` - 组合三限盘
   - `compositesecondary_chart_content` - 组合次限盘
   - `marks_a_chart_content` - 马克思盘-a语料
   - `marks_b_chart_content` - 马克思盘-b语料
   - `marksthirprogr_a_chart_content` - 马盘三限盘-a语料
   - `marksthirprogr_b_chart_content` - 马盘三限盘-b语料
   - `markssecprogr_a_chart_content` - 马盘次限盘-a语料
   - `markssecprogr_b_chart_content` - 马盘次限盘-b语料
   - `timesmidpoint_chart_content` - 时空盘语料
   - `timesmidpointthirprogr_chart_content` - 时空三限盘语料
   - `timesmidpointsecprogr_chart_content` - 时空次限盘语料

3. **Markdown字段**：
   - `chart_content_markdown` - Markdown格式语料

4. **普通文本字段**：
   - `title` - 标题

## 使用方法

### 运行翻译工具
```bash
python3 translate_corpus_en.py
```

### 功能特点

1. **智能内容识别**
   - 自动识别不同类型的内容格式
   - 针对富文本、JSON、Markdown、普通文本使用不同的翻译策略

2. **安全性保障**
   - 执行前预览需要翻译的记录
   - 需要用户确认才执行实际翻译
   - 完整的事务处理和错误回滚

3. **进度跟踪**
   - 添加翻译状态字段跟踪处理进度
   - 详细的日志记录
   - 实时显示处理进度

4. **格式保持**
   - 富文本：保持HTML标签和格式不变
   - JSON：保持JSON结构完整
   - Markdown：保持Markdown格式不变
   - 普通文本：直接翻译

5. **专业术语处理**
   - 针对占星学术语使用专业词汇翻译
   - 确保翻译的准确性和专业性

## 翻译策略

### 富文本字段 (content)
- 保持HTML标签和格式完全不变
- 只翻译文本内容部分
- 使用占星学专业术语

### JSON字段 (各种*_content字段)
- 保持JSON结构完全不变
- 只翻译JSON值中的中文部分
- 自动去除API返回的代码块标记

### Markdown字段 (chart_content_markdown)
- 保持Markdown格式完全不变
- 翻译文本内容
- 保留所有格式标记

### 普通文本字段 (title)
- 直接翻译文本内容
- 使用占星学专业术语

## 配置说明

### 数据库配置
- 主机: 59.110.52.91
- 端口: 13956
- 用户: horoscope_rwu
- 数据库: horoscope_prod

### API配置
- 使用 DeepSeek API 进行翻译
- 自动处理API限制和错误重试
- 每个字段翻译后有1秒延迟

## 日志文件
- `translate_corpus_en.log` - 详细的处理日志

## 注意事项

1. **数据备份**：建议在执行前备份数据库
2. **网络连接**：确保网络连接稳定，API调用较多
3. **处理时间**：大量数据翻译需要较长时间
4. **API限制**：注意API调用频率限制
5. **数据验证**：翻译完成后建议抽查结果质量

## 测试模式
如需测试，可以修改 main() 函数中的调用：
```python
translator.run_translation(limit=10)  # 只处理前10条记录
```

## 状态字段说明
工具会自动添加以下状态跟踪字段：
- `translation_status`: 翻译状态 (processing/completed/failed)
- `translation_progress`: 翻译进度详情
- `translation_updated_at`: 最后更新时间