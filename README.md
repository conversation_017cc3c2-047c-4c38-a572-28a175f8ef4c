# 数据库翻译工具

这个工具用于将数据库中的中文内容翻译成英文，支持断点续传功能。

## 功能特点

1. **智能识别中文内容**：自动检测需要翻译的中文字符
2. **断点续传**：支持从中断的地方继续执行翻译
3. **状态跟踪**：记录每条记录的翻译状态和进度
4. **错误处理**：完善的错误处理和日志记录
5. **API限制处理**：自动延迟避免API调用限制

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 开始翻译任务

```bash
python run_translation.py --action translate
```

### 2. 查看翻译状态

```bash
python run_translation.py --action status
```

### 3. 重置失败的记录

```bash
python run_translation.py --action reset-failed
```

## 配置说明

### 数据库配置
- 主机：************:13956
- 用户名：horoscope_rwu
- 密码：h46eaN6JoQxo0VIe
- 表名：corpus_astro_en

### API配置
- DeepSeek API Key：***********************************
- API地址：https://api.deepseek.com/v1/chat/completions

### 翻译字段
- title：标题字段
- sentence：句子字段
- chart_content_json：JSON格式图表内容
- chart_content_markdown：Markdown格式图表内容

## 翻译状态说明

工具会自动在数据库中添加以下字段来跟踪翻译状态：

- `translation_status`：翻译状态
  - `NULL`：未处理
  - `processing`：处理中
  - `completed`：已完成
  - `failed`：失败

- `translation_progress`：翻译进度详情
  - `field_name:translated`：字段已翻译
  - `field_name:no_chinese`：字段无中文内容
  - `field_name:empty`：字段为空
  - `field_name:failed`：字段翻译失败

- `translation_updated_at`：最后更新时间

## 翻译提示词设计

### JSON格式内容翻译提示词
```
请将以下内容中的中文翻译成英文，保持JSON格式不变，只翻译值中的中文内容：

要求：
1. 保持JSON结构完整
2. 只翻译中文内容，英文内容保持不变
3. 保持专业术语的准确性
4. 如果是占星学相关内容，请使用专业的占星学术语
5. 返回完整的JSON格式
```

### Markdown格式内容翻译提示词
```
请将以下Markdown格式内容中的中文翻译成英文，保持Markdown格式不变：

要求：
1. 保持Markdown格式和结构
2. 只翻译中文内容，英文内容保持不变
3. 保持专业术语的准确性
4. 如果是占星学相关内容，请使用专业的占星学术语
5. 返回完整的Markdown格式
```

### 普通文本翻译提示词
```
请将以下中文内容翻译成英文：

要求：
1. 保持原文的语气和风格
2. 使用自然流畅的英文表达
3. 如果是占星学相关内容，请使用专业的占星学术语
4. 保持内容的完整性和准确性
```

## 断点续传机制

1. 工具会自动检查数据库中的翻译状态
2. 只处理未翻译或翻译失败的记录
3. 每条记录的翻译进度都会被记录
4. 可以随时中断和重新开始翻译任务

## 日志文件

翻译过程中的所有操作都会记录在 `translation.log` 文件中，包括：
- 翻译进度
- 成功/失败记录
- 错误信息
- API调用详情

## 注意事项

1. **API限制**：工具会自动在API调用之间添加延迟，避免触发限制
2. **数据备份**：建议在运行前备份数据库
3. **网络稳定**：确保网络连接稳定，避免API调用失败
4. **字符编码**：确保数据库和文件都使用UTF-8编码

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 确认数据库配置信息
   - 检查防火墙设置

2. **API调用失败**
   - 检查API Key是否有效
   - 确认网络可以访问DeepSeek API
   - 检查API调用频率限制

3. **翻译结果异常**
   - 查看日志文件了解详细错误
   - 检查原文内容格式
   - 确认提示词是否合适

### 重新开始翻译

如果需要重新翻译所有内容：

```bash
# 重置所有翻译状态
python -c "
from translate_database import DatabaseTranslator
t = DatabaseTranslator()
t.connect_database()
t.cursor.execute('UPDATE corpus_astro_en SET translation_status = NULL, translation_progress = NULL, translation_updated_at = NULL')
t.connection.commit()
t.close_database()
print('所有翻译状态已重置')
"
```

## 性能优化

- 每处理10条记录后会自动休息5秒
- 每个字段翻译后会延迟1秒
- 使用批量更新减少数据库操作
- 智能跳过无需翻译的内容